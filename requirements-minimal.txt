# Essential packages only (tested and working versions)
numpy==1.24.4
pandas==2.2.2
scikit-learn==1.6.1
tensorflow==2.8.4  # Downgraded for TF 1.x compatibility with TFT model
protobuf==3.19.6   # Compatible with TensorFlow 2.8.4
lasio>=0.30        # For LAS file reading
matplotlib>=3.0    # For visualizations

# COMPATIBILITY NOTES:
# TensorFlow 2.8.4 + protobuf 3.19.6 are required for TFT model compatibility
