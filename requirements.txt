# Core ML dependencies (tested and working versions)
numpy==1.24.4
pandas==2.2.2
scikit-learn==1.6.1
tensorflow==2.8.4  # Downgraded for TF 1.x compatibility with TFT model
protobuf==3.19.6   # Compatible with TensorFlow 2.8.4

# TensorFlow ecosystem
tensorflow-probability==0.25.0
tensorflow-estimator==2.8.0

# Data processing utilities
wget==3.2
pyunpack==0.3
patool>=1.12

# Visualization
matplotlib==3.10.1
seaborn==0.13.2

# Well log data processing
lasio>=0.30  # For LAS file reading

# IMPORTANT COMPATIBILITY NOTES:
# - TensorFlow 2.8.4 is required for TF 1.x compatibility mode used by TFT model
# - protobuf 3.19.6 is the latest version compatible with TensorFlow 2.8.4
# - DO NOT upgrade Tensor<PERSON>low to 2.16+ as it breaks TFT model training
# - These versions have been tested and confirmed working with the TFT Well project
