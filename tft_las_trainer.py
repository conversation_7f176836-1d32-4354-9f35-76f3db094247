#!/usr/bin/env python3
"""
Complete TFT LAS Trainer - Standalone script for training and validating 
Temporal Fusion Transformer with LAS well log data.

This script provides:
1. Interactive LAS file selection
2. LAS data loading and processing with lasio
3. TFT model training and validation
4. Prediction visualization and plotting
5. Performance metrics and evaluation

Author: TFT LAS Trainer
Date: 2025-06-27
"""

import os
import sys
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

# Import LAS processing
try:
    import lasio
except ImportError:
    print("Error: lasio library not found. Please install with: pip install lasio")
    sys.exit(1)

# Import TensorFlow with compatibility handling
try:
    # TensorFlow import - Pyla<PERSON> may show warnings but code will work if TF is installed
    import tensorflow.compat.v1 as tf  # type: ignore[import]

    # Use TensorFlow 2.x but with v1 compatibility for the TFT model
    # This provides a balance between modern TF 2.x and model compatibility
    tf.compat.v1.disable_v2_behavior()
except ImportError:
    print("Error: TensorFlow not found. Please install TensorFlow.")
    sys.exit(1)

# Import TFT components
try:
    import data_formatters.welllog
    import libs.tft_model
    import libs.utils as utils
    import expt_settings.configs
except ImportError as e:
    print(f"Error importing TFT components: {e}")
    print("Make sure you're running from the TFT root directory.")
    sys.exit(1)


class LASDataProcessor:
    """Handles LAS file loading and processing for TFT."""
    
    def __init__(self, las_directory: str = "Las data"):
        self.las_directory = las_directory
        self.null_value = -999.25
        
        # Curve name mappings
        self.curve_mappings = {
            'gamma_ray': ['GR', 'GAMMA_RAY', 'GAMMA', 'SGR'],
            'resistivity': ['RT', 'RES', 'RESISTIVITY', 'ILD', 'LLD'],
            'density': ['RHOB', 'DENS', 'DENSITY', 'RHOZ'],
            'neutron': ['NPHI', 'NEUT', 'NEUTRON', 'PHIN'],
            'p_wave': ['P-WAVE', 'PWAVE', 'VP', 'PVEL', 'DT'],
            'depth': ['DEPTH', 'DEPT', 'MD', 'TVDSS']
        }
    
    def get_available_files(self) -> List[str]:
        """Get list of available LAS files."""
        pattern = os.path.join(self.las_directory, "*.las")
        files = glob.glob(pattern)
        return sorted(files)
    
    def load_las_file(self, file_path: str) -> Optional[pd.DataFrame]:
        """Load and process a single LAS file."""
        try:
            print(f"Loading: {os.path.basename(file_path)}")
            las = lasio.read(file_path)

            # Debug: print available curves
            print(f"Available curves in {os.path.basename(file_path)}: {list(las.curves.keys())}")

            # Extract well name
            well_name = self._extract_well_name(las, file_path)

            # Convert to DataFrame
            df = las.df()
            df.reset_index(inplace=True)

            # Debug: print DataFrame info
            print(f"DataFrame columns: {df.columns.tolist()}")
            print(f"DataFrame shape: {df.shape}")

            # Process curves
            df_processed = self._extract_and_standardize_curves(df, well_name)

            if df_processed is not None:
                print(f"Loaded {len(df_processed)} samples from {well_name}")
                return df_processed
            else:
                print(f"Failed to extract required curves from {well_name}")
                return None

        except Exception as e:
            print(f"Error loading {file_path}: {str(e)}")
            return None
    
    def _extract_well_name(self, las, file_path: str) -> str:
        """Extract well name from LAS file or filename."""
        try:
            if hasattr(las, 'well') and hasattr(las.well, 'WELL'):
                return str(las.well.WELL.value)
            elif hasattr(las, 'header') and 'WELL' in las.header:
                return str(las.header['WELL'])
        except:
            pass
        
        # Fallback to filename
        return os.path.splitext(os.path.basename(file_path))[0]
    
    def _extract_and_standardize_curves(self, df: pd.DataFrame, well_name: str) -> Optional[pd.DataFrame]:
        """Extract and standardize curve names."""
        output_data: Dict[str, Any] = {'well_id': well_name}
        
        for standard_name, possible_names in self.curve_mappings.items():
            curve_data = self._find_curve(df, possible_names)
            
            if curve_data is not None:
                if standard_name == 'p_wave':
                    curve_data = self._process_p_wave(curve_data, possible_names)
                output_data[standard_name] = curve_data
            else:
                print(f"Warning: Could not find {standard_name} curve in {well_name}")
                return None
        
        # Create DataFrame
        result_df = pd.DataFrame(output_data)
        result_df = self._clean_data(result_df)
        result_df = self._add_derived_features(result_df)
        
        return result_df
    
    def _find_curve(self, df: pd.DataFrame, possible_names: List[str]) -> Optional[np.ndarray]:
        """Find curve in DataFrame using possible names."""
        df_columns_upper = [col.upper() for col in df.columns]

        for name in possible_names:
            name_upper = name.upper()
            if name_upper in df_columns_upper:
                actual_col = df.columns[df_columns_upper.index(name_upper)]
                curve_data = np.asarray(df[actual_col].values)
                print(f"Found curve '{actual_col}' with {len(curve_data)} samples, {np.isnan(curve_data).sum()} NaN values")
                if len(curve_data) > 0 and not np.all(np.isnan(curve_data)):
                    print(f"Curve '{actual_col}' range: {np.nanmin(curve_data):.2f} to {np.nanmax(curve_data):.2f}")
                return curve_data

        print(f"Could not find any of these curves: {possible_names}")
        return None
    
    def _process_p_wave(self, curve_data: np.ndarray, possible_names: List[str]) -> np.ndarray:
        """Process P-wave data, converting from slowness if needed."""
        # Debug: print original values
        print(f"Original P-wave data range: {np.nanmin(curve_data):.2f} to {np.nanmax(curve_data):.2f}")
        print(f"P-wave curve names found: {possible_names}")

        # Only convert if the actual curve found was DT (slowness)
        # Check the actual curve name that was found, not the entire possible names list
        curve_name = None
        for name in possible_names:
            if name.upper() == 'DT' and np.nanmean(curve_data) > 100:  # DT values are typically > 100 μs/ft
                curve_name = 'DT'
                break
            elif name.upper() in ['P-WAVE', 'PWAVE', 'VP', 'PVEL'] and np.nanmean(curve_data) < 10000:  # P-wave values are typically < 10000 m/s
                curve_name = 'P-WAVE'
                break

        if curve_name == 'DT':
            print(f"Converting DT (slowness) to velocity...")
            # Convert from slowness (μs/ft) to velocity (ft/s)
            # Velocity = 1,000,000 / slowness
            valid_mask = (curve_data > 0) & (curve_data != self.null_value) & ~np.isnan(curve_data)
            curve_data[valid_mask] = 1000000.0 / curve_data[valid_mask]
            print(f"After DT conversion: {np.nanmin(curve_data):.2f} to {np.nanmax(curve_data):.2f}")
        else:
            print(f"Data is already P-wave velocity, no conversion needed")

        print(f"Final P-wave data range: {np.nanmin(curve_data):.2f} to {np.nanmax(curve_data):.2f}")
        return curve_data
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean data by handling null values and outliers."""
        # Replace null values with NaN
        df = df.replace(self.null_value, np.nan)
        
        # Remove rows with any NaN values
        df_clean = df.dropna()
        
        # Remove outliers (values beyond 3 standard deviations)
        numeric_columns = df_clean.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col not in ['well_id', 'depth']:
                mean = df_clean[col].mean()
                std = df_clean[col].std()
                df_clean = df_clean[
                    (df_clean[col] >= mean - 3*std) & 
                    (df_clean[col] <= mean + 3*std)
                ]
        
        return df_clean.reset_index(drop=True)
    
    def _add_derived_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add derived features for TFT."""
        # Normalize depth to 0-1 range for each well
        df['depth_normalized'] = (df['depth'] - df['depth'].min()) / (df['depth'].max() - df['depth'].min())

        # Apply log transformation to resistivity (logarithmic scale)
        if 'resistivity' in df.columns:
            # Ensure positive values for log transformation
            resistivity_data = df['resistivity'].copy()

            # Handle any zero or negative values by setting a minimum threshold
            min_resistivity = 0.01  # 0.01 ohm-m minimum threshold
            resistivity_data = np.maximum(resistivity_data, min_resistivity)

            # Apply log10 transformation
            df['log_resistivity'] = np.log10(resistivity_data)

            print(f"Applied log transformation to resistivity:")
            print(f"  Original range: {df['resistivity'].min():.3f} - {df['resistivity'].max():.3f} ohm-m")
            print(f"  Log10 range: {df['log_resistivity'].min():.3f} - {df['log_resistivity'].max():.3f}")

            # Replace original resistivity with log-transformed version for training
            df['resistivity'] = df['log_resistivity']
            df.drop('log_resistivity', axis=1, inplace=True)

        return df


class TFTLASTrainer:
    """Main trainer class for TFT with LAS data."""
    
    def __init__(self, las_directory: str = "Las data", output_dir: str = "tft_results"):
        self.las_directory = las_directory
        self.output_dir = output_dir
        self.processor = LASDataProcessor(las_directory)
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize TFT components
        self.formatter = data_formatters.welllog.WellLogFormatter()
        self.model_class = libs.tft_model.TemporalFusionTransformer
    
    def select_files_interactive(self) -> List[str]:
        """Interactive file selection interface."""
        available_files = self.processor.get_available_files()
        
        if not available_files:
            print(f"No LAS files found in {self.las_directory}")
            return []
        
        print("\nAvailable LAS files:")
        print("=" * 50)
        for i, file_path in enumerate(available_files, 1):
            print(f"{i:2d}. {os.path.basename(file_path)}")
        
        print("\nSelection options:")
        print("- Enter file numbers (e.g., 1,3,5 or 1-3)")
        print("- Enter 'all' to select all files")
        print("- Enter 'quit' to exit")
        
        while True:
            selection = input("\nEnter your selection: ").strip().lower()
            
            if selection == 'quit':
                return []
            elif selection == 'all':
                return available_files
            else:
                try:
                    selected_files = []
                    for part in selection.split(','):
                        part = part.strip()
                        if '-' in part:
                            start, end = map(int, part.split('-'))
                            for i in range(start, end + 1):
                                if 1 <= i <= len(available_files):
                                    selected_files.append(available_files[i-1])
                        else:
                            i = int(part)
                            if 1 <= i <= len(available_files):
                                selected_files.append(available_files[i-1])
                    
                    if selected_files:
                        print(f"\nSelected {len(selected_files)} files:")
                        for f in selected_files:
                            print(f"  - {os.path.basename(f)}")
                        return selected_files
                    else:
                        print("No valid files selected. Please try again.")
                        
                except ValueError:
                    print("Invalid input format. Please try again.")

    def load_and_combine_data(self, file_paths: List[str]) -> Optional[pd.DataFrame]:
        """Load and combine multiple LAS files."""
        all_data = []

        for file_path in file_paths:
            df = self.processor.load_las_file(file_path)
            if df is not None:
                all_data.append(df)

        if not all_data:
            print("No data could be loaded from selected files.")
            return None

        # Combine all data
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f"\nCombined dataset: {len(combined_df)} samples from {len(all_data)} wells")

        return combined_df

    def train_model(self, data: pd.DataFrame, test_mode: bool = True) -> Tuple[Any, Dict, pd.DataFrame, Dict]:
        """Train TFT model on the data."""
        print("\nPreparing data for TFT training...")

        # Split data
        train_data, valid_data, test_data = self.formatter.split_data(data)
        print(f"Data split - Train: {len(train_data)}, Valid: {len(valid_data)}, Test: {len(test_data)}")

        # Fit scalers on training data
        self.formatter.set_scalers(train_data)

        # Transform data
        train_formatted = self.formatter.transform_inputs(train_data)
        valid_formatted = self.formatter.transform_inputs(valid_data)
        test_formatted = self.formatter.transform_inputs(test_data)

        # Get model parameters - experiment_params contains all the required TFT parameters
        model_params = self.formatter.get_default_model_params()
        experiment_params = self.formatter.get_experiment_params()

        # Merge experiment parameters into model parameters for TFT initialization
        model_params.update(experiment_params)

        # Override for testing mode (faster training)
        if test_mode:
            experiment_params["num_epochs"] = 5
            model_params["hidden_layer_size"] = 32
            model_params["num_heads"] = 2

        model_params["model_folder"] = os.path.join(self.output_dir, "model")

        print(f"\nTraining TFT model for {experiment_params['num_epochs']} epochs...")

        # Train model
        try:
            # Reset default graph for clean state
            tf.reset_default_graph()

            # Get TensorFlow configuration
            import libs.utils as utils
            tf_config = utils.get_default_tensorflow_config(tf_device="cpu")

            # Use TensorFlow v1 style session with proper configuration
            with tf.Graph().as_default(), tf.Session(config=tf_config) as sess:
                # Explicitly disable eager execution in this context
                tf.compat.v1.disable_eager_execution()

                # Set Keras backend session for compatibility
                try:
                    tf.keras.backend.set_session(sess)
                except (AttributeError, RuntimeError):
                    # TensorFlow 2.x handles sessions automatically
                    print("Using TensorFlow 2.x automatic session management")

                return self._train_with_session(sess, train_formatted, valid_formatted, test_formatted,
                                              test_data, model_params, experiment_params, test_mode)
        except Exception as e:
            print(f"Training failed: {str(e)}")
            raise

    def _train_with_session(self, sess, train_formatted, valid_formatted, test_formatted,
                           test_data, model_params, experiment_params, test_mode):
        """Train model using TensorFlow v1 session."""
        model = self.model_class(model_params, use_cudnn=False)

        # Cache training data
        train_samples, valid_samples = self.formatter.get_num_samples_for_calibration()
        if test_mode:
            train_samples, valid_samples = min(1000, train_samples), min(100, valid_samples)

        model.cache_batched_data(train_formatted, "train", num_samples=train_samples)
        model.cache_batched_data(valid_formatted, "valid", num_samples=valid_samples)

        # Initialize TensorFlow variables - crucial for TF v1 compatibility
        sess.run(tf.global_variables_initializer())

        # Train
        model.fit()

        # Make predictions on test set
        print("\nMaking predictions on test set...")
        test_predictions = model.predict(test_formatted, return_targets=True)

        training_results = {
            'train_samples': len(train_formatted),
            'valid_samples': len(valid_formatted),
            'test_samples': len(test_formatted),
            'epochs': experiment_params["num_epochs"],
            'model_params': model_params
        }

        return model, training_results, test_data, test_predictions

    def _train_with_eager(self, train_formatted, valid_formatted, test_formatted,
                         test_data, model_params, experiment_params, test_mode):
        """Train model using TensorFlow 2.x eager execution."""
        # Simplified training for TF 2.x compatibility
        print("Note: Using simplified training mode for TensorFlow 2.x")

        # Create mock results for demonstration
        training_results = {
            'train_samples': len(train_formatted),
            'valid_samples': len(valid_formatted),
            'test_samples': len(test_formatted),
            'epochs': experiment_params["num_epochs"],
            'model_params': model_params,
            'note': 'Simplified mode - full TF2.x support requires model adaptation'
        }

        # Create mock predictions for demonstration
        test_predictions = {
            'predictions': np.random.normal(0, 1, (len(test_formatted), 1)),
            'targets': test_data['p_wave'].values.reshape(-1, 1)
        }

        return None, training_results, test_data, test_predictions

    def evaluate_predictions(self, test_data: pd.DataFrame, predictions: Dict) -> Dict:
        """Evaluate prediction quality."""
        print("\nEvaluating predictions...")

        # Debug: print available keys
        print(f"Available prediction keys: {list(predictions.keys())}")

        # Extract predictions and targets (use median prediction p50)
        pred_df = predictions['p50']  # Use median prediction
        target_df = predictions['targets']

        # Debug: check data types and shapes
        print(f"Prediction data type: {type(pred_df)}")
        print(f"Target data type: {type(target_df)}")
        print(f"Prediction columns: {pred_df.columns.tolist()}")
        print(f"Target columns: {target_df.columns.tolist()}")

        # Extract numeric values from the forecast columns (t+0, t+1, etc.)
        pred_cols = [col for col in pred_df.columns if col.startswith('t+')]
        target_cols = [col for col in target_df.columns if col.startswith('t+')]

        pred_values = pred_df[pred_cols].values.flatten()
        target_values = target_df[target_cols].values.flatten()

        # Debug: check raw prediction values before processing
        print(f"Raw prediction range: {pred_values.min():.4f} to {pred_values.max():.4f}")
        print(f"Raw target range: {target_values.min():.4f} to {target_values.max():.4f}")

        # Convert to numeric and remove any NaN values
        pred_values = pd.to_numeric(pred_values, errors='coerce')
        target_values = pd.to_numeric(target_values, errors='coerce')

        # Remove NaN pairs
        valid_mask = ~(np.isnan(pred_values) | np.isnan(target_values))
        pred_values = pred_values[valid_mask]
        target_values = target_values[valid_mask]

        print(f"Valid prediction samples: {len(pred_values)}")
        print(f"Final prediction range: {pred_values.min():.2f} to {pred_values.max():.2f}")
        print(f"Final target range: {target_values.min():.2f} to {target_values.max():.2f}")

        # Calculate metrics
        mse = np.mean((pred_values - target_values) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(pred_values - target_values))

        # Calculate R²
        ss_res = np.sum((target_values - pred_values) ** 2)
        ss_tot = np.sum((target_values - np.mean(target_values)) ** 2)
        r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

        metrics = {
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'num_predictions': len(pred_values)
        }

        print(f"Evaluation Results:")
        print(f"  RMSE: {rmse:.4f}")
        print(f"  MAE:  {mae:.4f}")
        print(f"  R²:   {r2:.4f}")

        return metrics

    def create_input_qc_plot(self, data: pd.DataFrame):
        """Create QC plots for input LAS data to check data quality and units."""
        print("\nCreating input data QC plots...")

        # Store original resistivity data before log transformation for QC plotting
        original_resistivity = None
        if 'resistivity' in data.columns:
            # Check if resistivity has already been log-transformed
            resistivity_data = data['resistivity'].dropna()
            if len(resistivity_data) > 0:
                # If values are mostly between -2 and 4, it's likely already log-transformed
                if resistivity_data.min() > -3 and resistivity_data.max() < 5:
                    # Already log-transformed, convert back for display
                    original_resistivity = 10 ** resistivity_data
                    print("Note: Resistivity appears to be log-transformed, showing original values in QC plot")
                else:
                    # Not log-transformed yet
                    original_resistivity = resistivity_data

        # Create figure with subplots for all input curves
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('LAS Input Data QC - Quality Control', fontsize=16, fontweight='bold')

        # Define curve plotting parameters
        curve_configs = {
            'gamma_ray': {'ax': axes[0, 0], 'color': 'green', 'unit': 'API', 'typical_range': (0, 200)},
            'resistivity': {'ax': axes[0, 1], 'color': 'red', 'unit': 'ohm-m (original)', 'typical_range': (0.1, 1000)},
            'density': {'ax': axes[0, 2], 'color': 'blue', 'unit': 'g/cm³', 'typical_range': (1.5, 3.0)},
            'neutron': {'ax': axes[1, 0], 'color': 'orange', 'unit': 'v/v', 'typical_range': (0, 0.6)},
            'p_wave': {'ax': axes[1, 1], 'color': 'purple', 'unit': 'm/s', 'typical_range': (1500, 8000)},
            'depth': {'ax': axes[1, 2], 'color': 'black', 'unit': 'ft or m', 'typical_range': None}
        }

        # Plot each curve
        for curve_name, config in curve_configs.items():
            ax = config['ax']

            # Special handling for resistivity to show original values
            if curve_name == 'resistivity' and original_resistivity is not None:
                curve_data = original_resistivity.dropna()
            elif curve_name in data.columns:
                curve_data = data[curve_name].dropna()
            else:
                curve_data = None

            if curve_data is not None and len(curve_data) > 0:
                    # Create histogram
                    ax.hist(curve_data, bins=50, alpha=0.7, color=config['color'], edgecolor='black')

                    # Add statistics
                    mean_val = curve_data.mean()
                    median_val = curve_data.median()
                    std_val = curve_data.std()
                    min_val = curve_data.min()
                    max_val = curve_data.max()

                    # Add vertical lines for statistics
                    ax.axvline(mean_val, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_val:.2f}')
                    ax.axvline(median_val, color='orange', linestyle='--', linewidth=2, label=f'Median: {median_val:.2f}')

                    # Set title and labels
                    ax.set_title(f'{curve_name.replace("_", " ").title()}\n'
                               f'Range: {min_val:.2f} - {max_val:.2f} {config["unit"]}\n'
                               f'Std: {std_val:.2f}', fontsize=10)
                    ax.set_xlabel(f'{curve_name.replace("_", " ").title()} ({config["unit"]})')
                    ax.set_ylabel('Frequency')
                    ax.legend(fontsize=8)
                    ax.grid(True, alpha=0.3)

                    # Add typical range shading if available
                    if config['typical_range']:
                        ax.axvspan(config['typical_range'][0], config['typical_range'][1],
                                 alpha=0.2, color='gray', label='Typical Range')

                        # Check if data is within typical range
                        in_range = ((curve_data >= config['typical_range'][0]) &
                                  (curve_data <= config['typical_range'][1])).mean() * 100
                        ax.text(0.02, 0.98, f'{in_range:.1f}% in typical range',
                               transform=ax.transAxes, verticalalignment='top',
                               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

                    # Special handling for P-wave to detect unit issues
                    if curve_name == 'p_wave':
                        # Check if values suggest wrong units
                        if mean_val < 500:
                            ax.text(0.02, 0.85, 'WARNING: Values very low!\nMight be in km/s instead of m/s\nConsider multiplying by 1000',
                                   transform=ax.transAxes, verticalalignment='top',
                                   bbox=dict(boxstyle='round', facecolor='red', alpha=0.8),
                                   color='white', fontweight='bold')
                        elif mean_val > 15000:
                            ax.text(0.02, 0.85, 'WARNING: Values very high!\nMight be in ft/s instead of m/s\nConsider dividing by 3.28084',
                                   transform=ax.transAxes, verticalalignment='top',
                                   bbox=dict(boxstyle='round', facecolor='red', alpha=0.8),
                                   color='white', fontweight='bold')
                        elif 1500 <= mean_val <= 8000:
                            ax.text(0.02, 0.85, 'GOOD: Values in correct m/s range\n(typical for P-wave velocity)',
                                   transform=ax.transAxes, verticalalignment='top',
                                   bbox=dict(boxstyle='round', facecolor='green', alpha=0.8),
                                   color='white', fontweight='bold')
                        elif 500 <= mean_val < 1500:
                            ax.text(0.02, 0.85, 'CAUTION: Values low for typical rock\nCheck if data is correct',
                                   transform=ax.transAxes, verticalalignment='top',
                                   bbox=dict(boxstyle='round', facecolor='orange', alpha=0.8),
                                   color='white', fontweight='bold')
                        elif 8000 < mean_val <= 15000:
                            ax.text(0.02, 0.85, 'CAUTION: Values high for typical rock\nCheck if data is correct',
                                   transform=ax.transAxes, verticalalignment='top',
                                   bbox=dict(boxstyle='round', facecolor='orange', alpha=0.8),
                                   color='white', fontweight='bold')
            else:
                ax.text(0.5, 0.5, f'No valid data\nfor {curve_name}',
                       transform=ax.transAxes, ha='center', va='center',
                       fontsize=12, bbox=dict(boxstyle='round', facecolor='lightgray'))
                ax.set_title(f'{curve_name.replace("_", " ").title()} - NO DATA')

        plt.tight_layout()

        # Save QC plot
        qc_plot_path = os.path.join(self.output_dir, 'input_data_qc.png')
        plt.savefig(qc_plot_path, dpi=300, bbox_inches='tight')
        print(f"Input data QC plot saved to: {qc_plot_path}")

        # Show plot
        plt.show()

        # Print summary statistics
        self._print_data_summary(data)

    def _print_data_summary(self, data: pd.DataFrame):
        """Print detailed summary statistics for the input data."""
        print("\n" + "="*60)
        print("INPUT DATA SUMMARY STATISTICS")
        print("="*60)

        numeric_columns = ['gamma_ray', 'resistivity', 'density', 'neutron', 'p_wave', 'depth']

        for col in numeric_columns:
            if col in data.columns:
                col_data = data[col].dropna()
                if len(col_data) > 0:
                    # Special handling for resistivity (might be log-transformed)
                    if col == 'resistivity':
                        # Check if data is log-transformed
                        if col_data.min() > -3 and col_data.max() < 5:
                            print(f"\n{col.replace('_', ' ').title()} (Log10-transformed for training):")
                            print(f"  Count: {len(col_data):,}")
                            print(f"  Mean:  {col_data.mean():.4f} (log10)")
                            print(f"  Std:   {col_data.std():.4f} (log10)")
                            print(f"  Min:   {col_data.min():.4f} (log10)")
                            print(f"  25%:   {col_data.quantile(0.25):.4f} (log10)")
                            print(f"  50%:   {col_data.median():.4f} (log10)")
                            print(f"  75%:   {col_data.quantile(0.75):.4f} (log10)")
                            print(f"  Max:   {col_data.max():.4f} (log10)")

                            # Show original values
                            original_data = 10 ** col_data
                            print(f"  >>> Original Resistivity Values:")
                            print(f"      Mean:  {original_data.mean():.2f} ohm-m")
                            print(f"      Range: {original_data.min():.2f} - {original_data.max():.2f} ohm-m")
                        else:
                            print(f"\n{col.replace('_', ' ').title()} (Original values):")
                            print(f"  Count: {len(col_data):,}")
                            print(f"  Mean:  {col_data.mean():.4f}")
                            print(f"  Std:   {col_data.std():.4f}")
                            print(f"  Min:   {col_data.min():.4f}")
                            print(f"  25%:   {col_data.quantile(0.25):.4f}")
                            print(f"  50%:   {col_data.median():.4f}")
                            print(f"  75%:   {col_data.quantile(0.75):.4f}")
                            print(f"  Max:   {col_data.max():.4f}")
                    else:
                        print(f"\n{col.replace('_', ' ').title()}:")
                        print(f"  Count: {len(col_data):,}")
                        print(f"  Mean:  {col_data.mean():.4f}")
                        print(f"  Std:   {col_data.std():.4f}")
                        print(f"  Min:   {col_data.min():.4f}")
                        print(f"  25%:   {col_data.quantile(0.25):.4f}")
                        print(f"  50%:   {col_data.median():.4f}")
                        print(f"  75%:   {col_data.quantile(0.75):.4f}")
                        print(f"  Max:   {col_data.max():.4f}")

                    # Special analysis for P-wave
                    if col == 'p_wave':
                        print(f"  >>> P-wave Analysis:")
                        mean_val = col_data.mean()
                        if mean_val < 500:
                            print(f"      WARNING: Mean value {mean_val:.2f} suggests data might be in km/s")
                            print(f"      Consider multiplying by 1000 to convert to m/s")
                            print(f"      Expected range for m/s: 1500-8000 m/s")
                        elif mean_val > 15000:
                            print(f"      WARNING: Mean value {mean_val:.2f} suggests data might be in ft/s")
                            print(f"      Consider dividing by 3.28084 to convert to m/s")
                            print(f"      Expected range for m/s: 1500-8000 m/s")
                        elif 1500 <= mean_val <= 8000:
                            print(f"      GOOD: Mean value {mean_val:.2f} is in correct m/s range")
                            print(f"      This is typical for P-wave velocity in rocks")
                        elif 500 <= mean_val < 1500:
                            print(f"      CAUTION: Mean value {mean_val:.2f} is low for typical rocks")
                            print(f"      Check if this represents unconsolidated sediments or fluids")
                        elif 8000 < mean_val <= 15000:
                            print(f"      CAUTION: Mean value {mean_val:.2f} is high for typical rocks")
                            print(f"      Check if this represents very hard/crystalline rocks")

                        # Additional magnitude analysis
                        if mean_val > 1000:
                            print(f"      MAGNITUDE CHECK: Values appear to be in correct order of magnitude")
                        else:
                            print(f"      MAGNITUDE WARNING: Values might be too small by factor of 1000")
                else:
                    print(f"\n{col.replace('_', ' ').title()}: NO VALID DATA")
            else:
                print(f"\n{col.replace('_', ' ').title()}: NOT FOUND IN DATASET")

        print("\n" + "="*60)

    def create_visualizations(self, test_data: pd.DataFrame, predictions: Dict, metrics: Dict):
        """Create comprehensive visualizations."""
        print("\nCreating visualizations...")

        # Extract data (use same logic as evaluation)
        pred_df = predictions['p50']  # Use median prediction
        target_df = predictions['targets']

        # Extract numeric values from the forecast columns
        pred_cols = [col for col in pred_df.columns if col.startswith('t+')]
        target_cols = [col for col in target_df.columns if col.startswith('t+')]

        pred_values = pred_df[pred_cols].values.flatten()
        target_values = target_df[target_cols].values.flatten()

        # Convert to numeric and remove any NaN values
        pred_values = pd.to_numeric(pred_values, errors='coerce')
        target_values = pd.to_numeric(target_values, errors='coerce')

        # Remove NaN pairs
        valid_mask = ~(np.isnan(pred_values) | np.isnan(target_values))
        pred_values = pred_values[valid_mask]
        target_values = target_values[valid_mask]

        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('TFT Well Log Prediction Results', fontsize=16, fontweight='bold')

        # 1. Prediction vs Actual scatter plot
        ax1 = axes[0, 0]
        ax1.scatter(target_values, pred_values, alpha=0.6, s=20)
        min_val = min(target_values.min(), pred_values.min())
        max_val = max(target_values.max(), pred_values.max())
        ax1.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')
        ax1.set_xlabel('Actual P-wave Velocity')
        ax1.set_ylabel('Predicted P-wave Velocity')
        ax1.set_title(f'Prediction vs Actual (R² = {metrics["r2"]:.3f})')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. Residuals plot
        ax2 = axes[0, 1]
        residuals = pred_values - target_values
        ax2.scatter(target_values, residuals, alpha=0.6, s=20)
        ax2.axhline(y=0, color='r', linestyle='--', lw=2)
        ax2.set_xlabel('Actual P-wave Velocity')
        ax2.set_ylabel('Residuals (Predicted - Actual)')
        ax2.set_title('Residuals Plot')
        ax2.grid(True, alpha=0.3)

        # 3. Prediction distribution
        ax3 = axes[1, 0]
        ax3.hist(target_values, bins=30, alpha=0.7, label='Actual', density=True)
        ax3.hist(pred_values, bins=30, alpha=0.7, label='Predicted', density=True)
        ax3.set_xlabel('P-wave Velocity')
        ax3.set_ylabel('Density')
        ax3.set_title('Distribution Comparison')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. Time series plot (sample of predictions)
        ax4 = axes[1, 1]
        sample_size = min(200, len(pred_values))
        indices = np.linspace(0, len(pred_values)-1, sample_size, dtype=int)
        ax4.plot(indices, target_values[indices], 'b-', label='Actual', linewidth=2)
        ax4.plot(indices, pred_values[indices], 'r--', label='Predicted', linewidth=2)
        ax4.set_xlabel('Sample Index')
        ax4.set_ylabel('P-wave Velocity')
        ax4.set_title('Sample Predictions Over Depth')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # Save plot
        plot_path = os.path.join(self.output_dir, 'prediction_results.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to: {plot_path}")

        # Show plot
        plt.show()

        # Create metrics summary plot
        self._create_metrics_summary(metrics)

    def _create_metrics_summary(self, metrics: Dict):
        """Create a summary metrics visualization."""
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))

        metric_names = ['RMSE', 'MAE', 'R²']
        metric_values = [metrics['rmse'], metrics['mae'], metrics['r2']]
        colors = ['red', 'orange', 'green']

        bars = ax.bar(metric_names, metric_values, color=colors, alpha=0.7)
        ax.set_title('Model Performance Metrics', fontsize=14, fontweight='bold')
        ax.set_ylabel('Metric Value')
        ax.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar, value in zip(bars, metric_values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{value:.4f}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        # Save metrics plot
        metrics_path = os.path.join(self.output_dir, 'metrics_summary.png')
        plt.savefig(metrics_path, dpi=300, bbox_inches='tight')
        print(f"Metrics summary saved to: {metrics_path}")

        plt.show()

    def run_complete_workflow(self, test_mode: bool = True):
        """Run the complete TFT training and validation workflow."""
        print("=" * 80)
        print("TFT LAS TRAINER - Complete Workflow")
        print("=" * 80)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            # Step 1: Select files
            print("\nStep 1: Select LAS files for training")
            selected_files = self.select_files_interactive()

            if not selected_files:
                print("No files selected. Exiting.")
                return

            # Step 2: Load and combine data
            print("\nStep 2: Loading and processing LAS data")
            combined_data = self.load_and_combine_data(selected_files)

            if combined_data is None:
                print("Failed to load data. Exiting.")
                return

            # Step 2.5: Create input data QC plots
            print("\nStep 2.5: Creating input data QC plots")
            self.create_input_qc_plot(combined_data)

            # Step 3: Train model
            print("\nStep 3: Training TFT model")
            model, training_results, test_data, predictions = self.train_model(combined_data, test_mode)

            # Step 4: Evaluate predictions
            print("\nStep 4: Evaluating predictions")
            metrics = self.evaluate_predictions(test_data, predictions)

            # Step 5: Create visualizations
            print("\nStep 5: Creating visualizations")
            self.create_visualizations(test_data, predictions, metrics)

            # Step 6: Save results summary
            self._save_results_summary(selected_files, training_results, metrics)

            print("\n" + "=" * 80)
            print("WORKFLOW COMPLETED SUCCESSFULLY!")
            print("=" * 80)
            print(f"Results saved to: {self.output_dir}")
            print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            print(f"\nWorkflow failed: {str(e)}")
            import traceback
            traceback.print_exc()

    def _save_results_summary(self, selected_files: List[str], training_results: Dict, metrics: Dict):
        """Save a comprehensive results summary."""
        summary_path = os.path.join(self.output_dir, 'results_summary.txt')

        with open(summary_path, 'w') as f:
            f.write("TFT LAS TRAINER - RESULTS SUMMARY\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("FILES USED:\n")
            f.write("-" * 20 + "\n")
            for i, file_path in enumerate(selected_files, 1):
                f.write(f"{i:2d}. {os.path.basename(file_path)}\n")

            f.write(f"\nTRAINING CONFIGURATION:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Training samples: {training_results['train_samples']}\n")
            f.write(f"Validation samples: {training_results['valid_samples']}\n")
            f.write(f"Test samples: {training_results['test_samples']}\n")
            f.write(f"Epochs: {training_results['epochs']}\n")

            f.write(f"\nPERFORMANCE METRICS:\n")
            f.write("-" * 25 + "\n")
            f.write(f"RMSE: {metrics['rmse']:.6f}\n")
            f.write(f"MAE:  {metrics['mae']:.6f}\n")
            f.write(f"R²:   {metrics['r2']:.6f}\n")
            f.write(f"MSE:  {metrics['mse']:.6f}\n")
            f.write(f"Number of predictions: {metrics['num_predictions']}\n")

            if 'note' in training_results:
                f.write(f"\nNOTES:\n")
                f.write("-" * 10 + "\n")
                f.write(f"{training_results['note']}\n")

        print(f"Results summary saved to: {summary_path}")


def main():
    """Main function to run the TFT LAS trainer."""
    import argparse

    parser = argparse.ArgumentParser(description='TFT LAS Trainer - Complete training and validation workflow')
    parser.add_argument('--las-dir', default='Las data',
                       help='Directory containing LAS files (default: Las data)')
    parser.add_argument('--output-dir', default='tft_results',
                       help='Output directory for results (default: tft_results)')
    parser.add_argument('--full-training', action='store_true',
                       help='Use full training mode (slower but more accurate)')
    parser.add_argument('--batch', action='store_true',
                       help='Run in batch mode (use all available files)')

    args = parser.parse_args()

    # Initialize trainer
    trainer = TFTLASTrainer(args.las_dir, args.output_dir)

    # Check if LAS files are available
    available_files = trainer.processor.get_available_files()
    if not available_files:
        print(f"No LAS files found in {args.las_dir}")
        print("Please ensure LAS files are available in the specified directory.")
        return 1

    print(f"Found {len(available_files)} LAS files in {args.las_dir}")

    if args.batch:
        # Batch mode: use all files
        print("Running in batch mode with all available files...")
        combined_data = trainer.load_and_combine_data(available_files)
        if combined_data is not None:
            # Create input data QC plots
            print("Creating input data QC plots...")
            trainer.create_input_qc_plot(combined_data)

            test_mode = not args.full_training
            model, training_results, test_data, predictions = trainer.train_model(combined_data, test_mode)
            metrics = trainer.evaluate_predictions(test_data, predictions)
            trainer.create_visualizations(test_data, predictions, metrics)
            trainer._save_results_summary(available_files, training_results, metrics)
    else:
        # Interactive mode
        test_mode = not args.full_training
        trainer.run_complete_workflow(test_mode)

    return 0


if __name__ == "__main__":
    exit(main())
