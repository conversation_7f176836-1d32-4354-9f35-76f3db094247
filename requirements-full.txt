# Core ML and Data Science (tested and working versions)
numpy==1.24.4
pandas==2.2.2
scikit-learn==1.6.1
tensorflow==2.8.4  # Downgraded for TF 1.x compatibility with TFT model
protobuf==3.19.6   # Compatible with TensorFlow 2.8.4
tensorflow-probability==0.25.0
tensorflow-estimator==2.8.0
tensorflow-io-gcs-filesystem==0.31.0

# Data processing and utilities
wget==3.2
pyunpack==0.3
patool==4.0.1
lasio>=0.30  # For LAS file reading

# Visualization
matplotlib==3.10.1
seaborn==0.13.2
plotly==6.0.0

# Jupyter and development tools
jupyter==1.1.1
ipykernel==6.29.5
notebook==7.3.3

# Additional scientific computing
scipy==1.15.3
h5py==3.13.0

# Optional: GPU support (if needed)
cupy-cuda117==10.6.0

# Development and code quality
black==25.1.0
flake8==7.1.2
autopep8==2.0.4

# IMPORTANT COMPATIBILITY NOTES:
# - TensorFlow 2.8.4 is required for TF 1.x compatibility mode used by TFT model
# - protobuf 3.19.6 is the latest version compatible with TensorFlow 2.8.4
# - DO NOT upgrade TensorFlow to 2.16+ as it breaks TFT model training
# - These versions have been tested and confirmed working with the TFT Well project
# - For GPU support, ensure CUDA 11.7 compatibility with cupy-cuda117
