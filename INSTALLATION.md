# TFT Well Project - Installation Guide

## Quick Start

### Option 1: Minimal Installation (Recommended)
```bash
pip install -r requirements-minimal.txt
```

### Option 2: Standard Installation
```bash
pip install -r requirements.txt
```

### Option 3: Full Development Environment
```bash
pip install -r requirements-full.txt
```

## Important Version Requirements

⚠️ **CRITICAL**: This project requires specific versions for compatibility:

- **TensorFlow 2.8.4** (NOT 2.16+)
- **protobuf 3.19.6** (NOT 4.x)

These versions are required because:
1. The TFT (Temporal Fusion Transformer) model uses TensorFlow 1.x compatibility mode
2. Newer TensorFlow versions have stricter eager execution that breaks the TFT model
3. protobuf 4.x is incompatible with TensorFlow 2.8.4

## Installation Steps

1. **Create a virtual environment** (recommended):
   ```bash
   python -m venv tft_env
   # Windows:
   tft_env\Scripts\activate
   # Linux/Mac:
   source tft_env/bin/activate
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify installation**:
   ```bash
   python -c "import tensorflow as tf; print('TensorFlow version:', tf.__version__)"
   python -c "import lasio; print('lasio imported successfully')"
   ```

## Troubleshooting

### TensorFlow Version Issues
If you see errors like:
- "`tf.data.Dataset` only supports Python-style iteration in eager mode"
- "Illegal number of inputs"

**Solution**: Ensure you have TensorFlow 2.8.4, not 2.16+:
```bash
pip uninstall tensorflow
pip install tensorflow==2.8.4 protobuf==3.19.6
```

### protobuf Compatibility Issues
If you see protobuf-related errors:
```bash
pip install protobuf==3.19.6
```

### LAS File Reading Issues
If lasio is not installed:
```bash
pip install lasio
```

## Testing the Installation

Run the TFT trainer in test mode:
```bash
python tft_las_trainer.py --batch
```

This should complete without errors and generate results in the `tft_results/` directory.

## GPU Support (Optional)

For GPU acceleration, ensure you have:
- CUDA 11.7 compatible drivers
- cupy-cuda117 (included in requirements-full.txt)

## Need Help?

If you encounter issues:
1. Check that you're using the exact versions specified
2. Try creating a fresh virtual environment
3. Ensure your Python version is 3.8-3.11 (recommended: 3.9)
